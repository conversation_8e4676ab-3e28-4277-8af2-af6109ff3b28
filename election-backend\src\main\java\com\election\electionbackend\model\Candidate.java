package com.election.electionbackend.model;

/**
 * Represents a candidate in an election
 */
public class Candidate {
    private String id;
    private String firstName;
    private String lastName;
    private String partyId;
    private int listPosition;
    private int votes;
    private boolean elected;

    public Candidate() {}

    public Candidate(String id, String firstName, String lastName, String partyId, int listPosition) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.partyId = partyId;
        this.listPosition = listPosition;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public String getPartyId() {
        return partyId;
    }

    public void setPartyId(String partyId) {
        this.partyId = partyId;
    }

    public int getListPosition() {
        return listPosition;
    }

    public void setListPosition(int listPosition) {
        this.listPosition = listPosition;
    }

    public int getVotes() {
        return votes;
    }

    public void setVotes(int votes) {
        this.votes = votes;
    }

    public boolean isElected() {
        return elected;
    }

    public void setElected(boolean elected) {
        this.elected = elected;
    }

    @Override
    public String toString() {
        return String.format("Candidate{id='%s', name='%s', party='%s', position=%d, votes=%d, elected=%s}", 
                id, getFullName(), partyId, listPosition, votes, elected);
    }
}
