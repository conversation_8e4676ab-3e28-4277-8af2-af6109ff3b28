<script setup>
// Import herbruikbare layout componenten
import NavBar from './components/layout/NavBar.vue'
import Footer from './components/layout/Footer.vue'
</script>

<template>
  <div id="app">
    <!-- Navbar - overal hetzelfde -->
    <NavBar />

    <!-- Main content area -->
    <main class="main-content">
      <div class="container">
        <header class="page-header">
          <h1>Welkom bij Home</h1>
        </header>

        <div class="home-content">
          <div class="welcome-section">
            <h2>Nederlandse Verkiezingen Dashboard</h2>
            <p>Welkom bij het officiële verkiezingsdashboard. Hier kunt u alle informatie vinden over de Nederlandse verkiezingen, partijen, beleid en meer.</p>
          </div>

          <div class="features-grid">
            <div class="feature-card">
              <h3>Policy's</h3>
              <p>Beki<PERSON> de beleidsstandpunten van alle politieke partijen</p>
            </div>

            <div class="feature-card">
              <h3>Kaart</h3>
              <p><PERSON>actie<PERSON> kaart met verkiezingsresultaten per regio</p>
            </div>

            <div class="feature-card">
              <h3>Partijen</h3>
              <p>Overzicht van alle deelnemende politieke partijen</p>
            </div>

            <div class="feature-card">
              <h3>Mijn vragen</h3>
              <p>Stel uw vragen over de verkiezingen en het proces</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer - overal hetzelfde -->
    <Footer />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem 0;
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #1e40af;
  margin: 0;
  font-weight: bold;
}

.welcome-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
  color: #374151;
  margin-bottom: 1rem;
}

.welcome-section p {
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.feature-card p {
  color: #6b7280;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
