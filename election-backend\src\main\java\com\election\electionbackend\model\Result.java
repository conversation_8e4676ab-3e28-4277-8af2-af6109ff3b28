// election-backend/src/main/java/com/election/electionbackend/model/Result.java
package com.election.electionbackend.model;

import java.util.Map;

public class Result {
    private final String partyName;
    private final int votes;
    private final String level;
    private final String regionName;

    public Result(Map<String, String> electionData) {
        this.partyName = electionData.getOrDefault("RegisteredName", "");
        this.votes = Integer.parseInt(electionData.getOrDefault("Count", "0"));
        this.level = electionData.getOrDefault("RegionCategory", "national");
        this.regionName = electionData.getOrDefault("RegionName", "");
    }

    public String getPartyName() {
        return partyName;
    }

    public int getVotes() {
        return votes;
    }

    public String getLevel() {
        return level;
    }

    public String getRegionName() {
        return regionName;
    }

    @Override
    public String toString() {
        return String.format("Result{party='%s', votes=%d, level='%s', region='%s'}",
                partyName, votes, level, regionName);
    }
}
