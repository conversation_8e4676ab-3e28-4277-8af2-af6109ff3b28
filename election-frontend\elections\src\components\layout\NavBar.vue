<script setup>
import { ref } from 'vue'

// Reactive data voor mobile menu
const isMenuOpen = ref(false)

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

// Navigation items
const navigationItems = [
  { name: 'Home', path: '/' },
  { name: "Policy's", path: '/policies' },
  { name: 'Ka<PERSON>', path: '/kaart' },
  { name: 'Partijen', path: '/partijen' },
  { name: 'Mijn vragen', path: '/mijn-vragen' }
]
</script>

<template>
  <nav class="navbar">
    <div class="nav-container">
      <!-- Logo/Brand -->
      <div class="nav-brand">
        <h1>Nederlandse Verkiezingen</h1>
      </div>

      <!-- Desktop Navigation -->
      <ul class="nav-menu" :class="{ 'active': isMenuOpen }">
        <li v-for="item in navigationItems" :key="item.name" class="nav-item">
          <a :href="item.path" class="nav-link">{{ item.name }}</a>
        </li>
      </ul>

      <!-- Mobile Menu Button -->
      <div class="nav-toggle" @click="toggleMenu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.navbar {
  background-color: #1e40af; /* Blauw */
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: block;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

/* Mobile Styles */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: white;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

@media screen and (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background-color: #1e40af;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.15);
    padding: 2rem 0;
    gap: 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    margin: 0.5rem 0;
  }

  .nav-link {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }

  .nav-toggle {
    display: flex;
  }

  /* Hamburger animation */
  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }
}
</style>
