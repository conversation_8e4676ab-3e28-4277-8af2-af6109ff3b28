<script setup>
// Props voor dynamische content
defineProps({
  title: {
    type: String,
    default: 'Nederlandse Verkiezingen'
  }
})

// Reactive data voor navigation
import { ref } from 'vue'

const isMenuOpen = ref(false)

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const navigationItems = [
  { name: 'Home', path: '/' },
  { name: 'Verkiezingen', path: '/elections' },
  { name: 'Resultaten', path: '/results' },
  { name: 'Statistieken', path: '/statistics' }
]
</script>

<template>
  <nav class="navbar">
    <div class="nav-container">
      <!-- Logo/Title -->
      <div class="nav-brand">
        <h1>{{ title }}</h1>
      </div>

      <!-- Desktop Navigation -->
      <ul class="nav-menu" :class="{ 'active': isMenuOpen }">
        <li v-for="item in navigationItems" :key="item.name" class="nav-item">
          <a :href="item.path" class="nav-link">{{ item.name }}</a>
        </li>
      </ul>

      <!-- Mobile Menu Button -->
      <div class="nav-toggle" @click="toggleMenu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.navbar {
  background-color: #1e3a8a;
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile Styles */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: white;
  margin: 3px 0;
  transition: 0.3s;
}

@media screen and (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: #1e3a8a;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    margin: 1rem 0;
  }

  .nav-toggle {
    display: flex;
  }
}
</style>
