<script setup>
// Footer component - herbruikbaar op elke pagina
const currentYear = new Date().getFullYear()
</script>

<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>Nederlandse Verkiezingen</h3>
          <p>Officiële verkiezingsresultaten en statistieken</p>
        </div>

        <div class="footer-section">
          <h4>Links</h4>
          <ul>
            <li><a href="https://www.kiesraad.nl" target="_blank">Kiesraad</a></li>
            <li><a href="https://data.overheid.nl" target="_blank">Data Overheid</a></li>
            <li><a href="/about">Over dit project</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>Contact</h4>
          <p>HBO-ICT Semester 3 Project</p>
          <p>Hogeschool van Amsterdam</p>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; {{ currentYear }} Nederlandse Verkiezingen. Alle rechten voorbehouden.</p>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.footer {
  background-color: #1f2937;
  color: white;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #60a5fa;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: #60a5fa;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 1rem;
  text-align: center;
  color: #9ca3af;
}

@media screen and (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}
</style>
