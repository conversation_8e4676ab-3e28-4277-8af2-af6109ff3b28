<script setup>
// Import herbruikbare layout componenten
import NavBar from '../components/layout/NavBar.vue'
import Footer from '../components/layout/Footer.vue'
import { ref } from 'vue'

// Reactive data voor policy's
const policies = ref([
  {
    id: 1,
    party: 'VVD',
    title: 'Economisch beleid',
    description: 'Lagere belastingen voor ondernemers en meer ruimte voor innovatie.',
    category: 'Economie'
  },
  {
    id: 2,
    party: 'PvdA',
    title: 'Sociale zekerheid',
    description: 'Versterking van het sociale vangnet en betere arbeidsvoorwaarden.',
    category: 'Sociaal'
  },
  {
    id: 3,
    party: 'D66',
    title: 'Onderwijs en innovatie',
    description: 'Meer investering in onderwijs en digitale innovatie.',
    category: 'Onderwijs'
  },
  {
    id: 4,
    party: 'GroenLinks',
    title: 'Klimaat en milieu',
    description: 'Vers<PERSON>ing van de energietransitie en duurzaamheidsmaatregelen.',
    category: 'Milieu'
  }
])

const selectedCategory = ref('Alle')
const categories = ref(['Alle', 'Economie', 'Sociaal', 'Onderwijs', 'Milieu'])

// Computed property voor gefilterde policies
const filteredPolicies = computed(() => {
  if (selectedCategory.value === 'Alle') {
    return policies.value
  }
  return policies.value.filter(policy => policy.category === selectedCategory.value)
})
</script>

<template>
  <div id="app">
    <!-- Navbar -->
    <NavBar />
    
    <!-- Main content area -->
    <main class="main-content">
      <div class="container">
        <header class="page-header">
          <h1>Welkom bij Policy's</h1>
        </header>
        
        <div class="policies-content">
          <div class="intro-section">
            <h2>Beleidsstandpunten van de Partijen</h2>
            <p>Hier vindt u een overzicht van de belangrijkste beleidsstandpunten van alle deelnemende politieke partijen.</p>
          </div>
          
          <!-- Filter sectie -->
          <div class="filter-section">
            <h3>Filter op categorie:</h3>
            <div class="filter-buttons">
              <button 
                v-for="category in categories" 
                :key="category"
                @click="selectedCategory = category"
                :class="{ 'active': selectedCategory === category }"
                class="filter-btn"
              >
                {{ category }}
              </button>
            </div>
          </div>
          
          <!-- Policies grid -->
          <div class="policies-grid">
            <div 
              v-for="policy in filteredPolicies" 
              :key="policy.id"
              class="policy-card"
            >
              <div class="policy-header">
                <h3>{{ policy.title }}</h3>
                <span class="party-badge">{{ policy.party }}</span>
              </div>
              <div class="policy-body">
                <p>{{ policy.description }}</p>
                <span class="category-tag">{{ policy.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <!-- Footer -->
    <Footer />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem 0;
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #1e40af;
  margin: 0;
  font-weight: bold;
}

.intro-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.intro-section h2 {
  color: #374151;
  margin-bottom: 1rem;
}

.intro-section p {
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
}

.filter-section {
  margin-bottom: 2rem;
  text-align: center;
}

.filter-section h3 {
  color: #374151;
  margin-bottom: 1rem;
}

.filter-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e5e7eb;
  background-color: white;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-btn:hover {
  border-color: #1e40af;
  color: #1e40af;
}

.filter-btn.active {
  background-color: #1e40af;
  border-color: #1e40af;
  color: white;
}

.policies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.policy-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.policy-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.policy-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.policy-header h3 {
  color: #1e40af;
  margin: 0;
  font-size: 1.25rem;
}

.party-badge {
  background-color: #1e40af;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.policy-body {
  padding: 1.5rem;
}

.policy-body p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.category-tag {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .policies-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .filter-btn {
    width: 200px;
  }
}
</style>
