<script setup>
import { ref, onMounted } from 'vue'

// Reactive data
const results = ref([])
const loading = ref(false)
const error = ref(null)

// Props
const props = defineProps({
  electionId: {
    type: String,
    required: true
  }
})

// Methods
const fetchResults = async () => {
  loading.value = true
  error.value = null
  
  try {
    // API call naar je Spring Boot backend
    const response = await fetch(`http://localhost:8080/elections/${props.electionId}`)
    
    if (!response.ok) {
      throw new Error('Failed to fetch election results')
    }
    
    const data = await response.json()
    results.value = data.results || []
  } catch (err) {
    error.value = err.message
    console.error('Error fetching results:', err)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchResults()
})

// Computed properties
const totalVotes = computed(() => {
  return results.value.reduce((total, result) => total + result.votes, 0)
})
</script>

<template>
  <div class="election-results">
    <h2>Verkiezingsresultaten</h2>
    
    <!-- Loading state -->
    <div v-if="loading" class="loading">
      <p>Resultaten laden...</p>
    </div>
    
    <!-- Error state -->
    <div v-else-if="error" class="error">
      <p>Fout bij laden: {{ error }}</p>
      <button @click="fetchResults" class="retry-btn">Opnieuw proberen</button>
    </div>
    
    <!-- Results -->
    <div v-else-if="results.length > 0" class="results-container">
      <div class="results-summary">
        <p><strong>Totaal aantal stemmen:</strong> {{ totalVotes.toLocaleString() }}</p>
      </div>
      
      <div class="results-grid">
        <div 
          v-for="result in results" 
          :key="result.partyName"
          class="result-card"
        >
          <h3>{{ result.partyName }}</h3>
          <div class="vote-count">
            {{ result.votes.toLocaleString() }} stemmen
          </div>
          <div class="percentage">
            {{ ((result.votes / totalVotes) * 100).toFixed(1) }}%
          </div>
        </div>
      </div>
    </div>
    
    <!-- No results -->
    <div v-else class="no-results">
      <p>Geen resultaten beschikbaar voor deze verkiezing.</p>
    </div>
  </div>
</template>

<style scoped>
.election-results {
  padding: 2rem;
}

.loading, .error, .no-results {
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

.error {
  background-color: #fee;
  color: #c53030;
}

.retry-btn {
  background-color: #3182ce;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.retry-btn:hover {
  background-color: #2c5aa0;
}

.results-summary {
  background-color: #e6f3ff;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.result-card {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.result-card h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.vote-count {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3182ce;
  margin-bottom: 0.5rem;
}

.percentage {
  font-size: 1.1rem;
  color: #718096;
}

@media (max-width: 768px) {
  .election-results {
    padding: 1rem;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
}
</style>
