package com.election.electionbackend.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a political party in an election
 */
public class Party {
    private String id;
    private String name;
    private String abbreviation;
    private String color; // For UI display
    private List<Candidate> candidates;
    private int totalVotes;

    public Party() {
        this.candidates = new ArrayList<>();
    }

    public Party(String id, String name, String abbreviation) {
        this();
        this.id = id;
        this.name = name;
        this.abbreviation = abbreviation;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public void setAbbreviation(String abbreviation) {
        this.abbreviation = abbreviation;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public List<Candidate> getCandidates() {
        return candidates;
    }

    public void setCandidates(List<Candidate> candidates) {
        this.candidates = candidates;
    }

    public int getTotalVotes() {
        return totalVotes;
    }

    public void setTotalVotes(int totalVotes) {
        this.totalVotes = totalVotes;
    }

    public void addCandidate(Candidate candidate) {
        this.candidates.add(candidate);
    }

    @Override
    public String toString() {
        return String.format("Party{id='%s', name='%s', abbreviation='%s', totalVotes=%d}", 
                id, name, abbreviation, totalVotes);
    }
}
